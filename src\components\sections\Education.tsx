'use client';

import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { GraduationCap, Calendar, MapPin, Award, BookOpen } from 'lucide-react';

const educationData = [
  {
    degree: "Master of Science in Biotechnology",
    institution: "St. Thomas College, Bhilai",
    period: "2022 – 2024",
    location: "Bhilai, India",
    grade: "70%",
    description: "Advanced studies in biotechnology with focus on research methodologies, environmental applications, and laboratory techniques.",
    highlights: [
      "Research in plant tissue culture",
      "Environmental biotechnology applications",
      "Advanced laboratory techniques",
      "Data analysis and interpretation"
    ]
  },
  {
    degree: "Bachelor of Science in Biotechnology",
    institution: "St. Thomas College, Bhilai", 
    period: "2019 – 2022",
    location: "Bhilai, India",
    grade: "73.6%",
    description: "Comprehensive undergraduate program covering fundamental principles of biotechnology, microbiology, and biochemistry.",
    highlights: [
      "Fundamental biotechnology principles",
      "Microbiology and biochemistry",
      "Laboratory skills development",
      "Scientific research foundation"
    ]
  }
];

const certifications = [
  {
    title: "ICMR-ROHC Research Facility Training",
    issuer: "DST-SERB",
    year: "2024",
    description: "Specialized training in research methodologies and facility management"
  },
  {
    title: "International Workshop on Data Science & ML in R",
    issuer: "International Organization",
    year: "2024",
    description: "Advanced training in data science applications using R programming"
  },
  {
    title: "International Workshop on Data Analytics in Biological Research",
    issuer: "Research Institute",
    year: "2024", 
    description: "Specialized workshop on applying data analytics in biological research"
  }
];

export default function Education() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
      },
    },
  };

  return (
    <section id="education" className="section-padding bg-gradient-to-b from-primary-dark/90 to-primary-dark">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-montserrat font-bold text-primary-light mb-6">
            Education & <span className="gradient-text">Certifications</span>
          </h2>
          <p className="text-lg text-primary-light/70 max-w-2xl mx-auto">
            Academic foundation and professional development in biotechnology
          </p>
        </motion.div>

        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="space-y-16"
        >
          {/* Education Section */}
          <div>
            <motion.h3 
              variants={itemVariants}
              className="text-2xl font-montserrat font-bold text-primary-aqua mb-8 flex items-center gap-3"
            >
              <GraduationCap className="w-8 h-8" />
              Academic Qualifications
            </motion.h3>

            <div className="space-y-8">
              {educationData.map((edu, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  whileHover={{ scale: 1.02, y: -5 }}
                  className="bg-primary-aqua/5 border border-primary-aqua/20 rounded-xl p-6 hover:border-primary-aqua/40 transition-all duration-300 glow-effect"
                >
                  <div className="grid md:grid-cols-3 gap-6">
                    {/* Left Column - Main Info */}
                    <div className="md:col-span-2">
                      <div className="flex items-start justify-between mb-4">
                        <div>
                          <h4 className="text-xl font-montserrat font-bold text-primary-light mb-2">
                            {edu.degree}
                          </h4>
                          <div className="flex items-center gap-4 text-primary-light/70 mb-2">
                            <div className="flex items-center gap-1">
                              <BookOpen size={16} />
                              <span className="font-medium">{edu.institution}</span>
                            </div>
                          </div>
                          <div className="flex items-center gap-4 text-primary-light/70">
                            <div className="flex items-center gap-1">
                              <Calendar size={16} />
                              <span>{edu.period}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <MapPin size={16} />
                              <span>{edu.location}</span>
                            </div>
                          </div>
                        </div>
                        
                        <div className="text-right">
                          <div className="flex items-center gap-1 text-primary-aqua">
                            <Award size={16} />
                            <span className="font-bold text-lg">{edu.grade}</span>
                          </div>
                        </div>
                      </div>

                      <p className="text-primary-light/80 mb-4">
                        {edu.description}
                      </p>

                      <div className="flex flex-wrap gap-2">
                        {edu.highlights.map((highlight, idx) => (
                          <span
                            key={idx}
                            className="px-3 py-1 bg-primary-aqua/10 border border-primary-aqua/30 rounded-full text-sm text-primary-aqua"
                          >
                            {highlight}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Right Column - Visual Element */}
                    <div className="flex items-center justify-center">
                      <div className="w-24 h-24 bg-gradient-to-br from-primary-aqua/20 to-primary-aqua/5 rounded-full flex items-center justify-center">
                        <GraduationCap className="w-12 h-12 text-primary-aqua" />
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Certifications Section */}
          <div>
            <motion.h3 
              variants={itemVariants}
              className="text-2xl font-montserrat font-bold text-primary-aqua mb-8 flex items-center gap-3"
            >
              <Award className="w-8 h-8" />
              Certifications & Workshops
            </motion.h3>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {certifications.map((cert, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  whileHover={{ scale: 1.05, y: -5 }}
                  className="bg-primary-aqua/5 border border-primary-aqua/20 rounded-xl p-6 hover:border-primary-aqua/40 transition-all duration-300 glow-effect"
                >
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-br from-primary-aqua/20 to-primary-aqua/5 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Award className="w-8 h-8 text-primary-aqua" />
                    </div>
                    
                    <h4 className="text-lg font-montserrat font-bold text-primary-light mb-2">
                      {cert.title}
                    </h4>
                    
                    <p className="text-primary-aqua font-medium mb-2">
                      {cert.issuer}
                    </p>
                    
                    <p className="text-primary-light/70 text-sm mb-3">
                      {cert.description}
                    </p>
                    
                    <span className="inline-block px-3 py-1 bg-primary-aqua/10 border border-primary-aqua/30 rounded-full text-sm text-primary-aqua font-medium">
                      {cert.year}
                    </span>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
