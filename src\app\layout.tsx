import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "next/font/google";
import "./globals.css";

const montserrat = Montserrat({
  variable: "--font-montserrat",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
});

const roboto = Roboto({
  variable: "--font-roboto",
  subsets: ["latin"],
  weight: ["300", "400", "500", "700"],
});

export const metadata: Metadata = {
  title: "<PERSON><PERSON><PERSON><PERSON> - Biotechnologist & Researcher",
  description: "Motivated biotechnology postgraduate with research experience at ICMR-NIV and ICMR-ROHC. Skilled in ELISA, DNA/RNA extraction, microbial culture, and environmental biotechnology.",
  keywords: "biotechnology, research, ICMR, ELISA, DNA extraction, environmental biotechnology, public health",
  authors: [{ name: "<PERSON><PERSON><PERSON><PERSON>" }],
  openGraph: {
    title: "<PERSON><PERSON><PERSON><PERSON>iotechnologist & Researcher",
    description: "Motivated biotechnology postgraduate with research experience at ICMR-NIV and ICMR-ROHC.",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${montserrat.variable} ${roboto.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
