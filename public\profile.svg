<svg width="320" height="320" viewBox="0 0 320 320" fill="none" xmlns="http://www.w3.org/2000/svg">
  <circle cx="160" cy="160" r="160" fill="url(#gradient)"/>
  <circle cx="160" cy="120" r="40" fill="#4ecdc4" opacity="0.8"/>
  <path d="M80 240 C80 200, 120 180, 160 180 C200 180, 240 200, 240 240 L240 320 L80 320 Z" fill="#4ecdc4" opacity="0.8"/>
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a2e45;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4ecdc4;stop-opacity:0.3" />
    </linearGradient>
  </defs>
</svg>
