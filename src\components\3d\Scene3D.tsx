'use client';

import { Canvas } from '@react-three/fiber';
import { OrbitControls, Environment } from '@react-three/drei';
import { Suspense } from 'react';
import DNAHelix from './DNAHelix';
import ParticleField from './ParticleField';

interface Scene3DProps {
  showDNA?: boolean;
  showParticles?: boolean;
  className?: string;
}

function SceneContent({ showDNA = true, showParticles = true }: Omit<Scene3DProps, 'className'>) {
  return (
    <>
      {/* Lighting */}
      <ambientLight intensity={0.3} />
      <directionalLight 
        position={[10, 10, 5]} 
        intensity={0.5} 
        color="#ffffff"
      />
      <pointLight 
        position={[-10, -10, -5]} 
        intensity={0.3} 
        color="#4ecdc4"
      />
      
      {/* 3D Objects */}
      {showDNA && <DNAHelix position={[0, 0, 0]} scale={0.8} speed={0.5} />}
      {showParticles && <ParticleField count={800} radius={15} />}
      
      {/* Environment */}
      <Environment preset="night" />
      
      {/* Controls (disabled for background use) */}
      <OrbitControls 
        enableZoom={false} 
        enablePan={false} 
        enableRotate={false}
        autoRotate
        autoRotateSpeed={0.5}
      />
    </>
  );
}

export default function Scene3D({ 
  showDNA = true, 
  showParticles = true, 
  className = "" 
}: Scene3DProps) {
  return (
    <div className={`w-full h-full ${className}`}>
      <Canvas
        camera={{ 
          position: [0, 0, 10], 
          fov: 60,
          near: 0.1,
          far: 1000
        }}
        style={{ 
          background: 'transparent',
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          zIndex: -1
        }}
      >
        <Suspense fallback={null}>
          <SceneContent showDNA={showDNA} showParticles={showParticles} />
        </Suspense>
      </Canvas>
    </div>
  );
}
