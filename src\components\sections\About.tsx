'use client';

import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { Microscope, FlaskConical, Dna } from 'lucide-react';
import Image from 'next/image';
import Scene3D from '../3d/Scene3D';

export default function About() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
      },
    },
  };

  const highlights = [
    {
      icon: <Microscope className="w-8 h-8" />,
      title: "Research Excellence",
      description: "Project Assistant at ICMR-NIV Bengaluru with hands-on research experience"
    },
    {
      icon: <FlaskConical className="w-8 h-8" />,
      title: "Laboratory Skills",
      description: "Proficient in ELISA, DNA/RNA extraction, and microbial culture techniques"
    },
    {
      icon: <Dna className="w-8 h-8" />,
      title: "Biotechnology Focus",
      description: "Specialized in environmental biotechnology and public health applications"
    }
  ];

  return (
    <section id="about" className="relative section-padding bg-gradient-to-b from-primary-dark to-primary-dark/90">
      {/* Subtle 3D Background */}
      <div className="absolute inset-0 opacity-20">
        <Scene3D showDNA={false} showParticles={true} />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="grid lg:grid-cols-2 gap-12 items-center"
        >
          {/* Left Column - Image */}
          <motion.div variants={itemVariants} className="relative">
            <div className="relative w-full max-w-md mx-auto">
              {/* Profile Image Container */}
              <div className="relative">
                <div className="w-80 h-80 rounded-full bg-gradient-to-br from-primary-aqua/20 to-primary-aqua/5 p-2 mx-auto">
                  <div className="w-full h-full rounded-full bg-primary-dark border-4 border-primary-aqua/30 overflow-hidden glow-effect">
                    {/* Profile Image */}
                    <Image
                      src="/profile.svg"
                      alt="Pratibha Madhukar"
                      width={320}
                      height={320}
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>
                
                {/* Floating Elements */}
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                  className="absolute -top-4 -right-4 w-16 h-16 bg-primary-aqua/20 rounded-full flex items-center justify-center"
                >
                  <Dna className="w-8 h-8 text-primary-aqua" />
                </motion.div>
                
                <motion.div
                  animate={{ y: [-10, 10, -10] }}
                  transition={{ duration: 4, repeat: Infinity }}
                  className="absolute -bottom-4 -left-4 w-12 h-12 bg-primary-aqua/20 rounded-full flex items-center justify-center"
                >
                  <Microscope className="w-6 h-6 text-primary-aqua" />
                </motion.div>
              </div>
            </div>
          </motion.div>

          {/* Right Column - Content */}
          <motion.div variants={itemVariants} className="space-y-8">
            <div>
              <motion.h2 
                variants={itemVariants}
                className="text-4xl md:text-5xl font-montserrat font-bold text-primary-light mb-6"
              >
                About <span className="gradient-text">Me</span>
              </motion.h2>
              
              <motion.p 
                variants={itemVariants}
                className="text-lg text-primary-light/80 leading-relaxed mb-6"
              >
                I am a motivated biotechnology postgraduate with extensive research experience at 
                prestigious institutions including ICMR-NIV and ICMR-ROHC. My expertise spans across 
                various biotechnological applications, with a particular focus on environmental 
                biotechnology and public health research.
              </motion.p>
              
              <motion.p 
                variants={itemVariants}
                className="text-lg text-primary-light/80 leading-relaxed mb-8"
              >
                Currently serving as a Project Assistant at ICMR-NIV Bengaluru, I am passionate about 
                leveraging biotechnology to address real-world challenges and contribute to scientific 
                advancement in public health and environmental sustainability.
              </motion.p>
            </div>

            {/* Highlights */}
            <motion.div variants={itemVariants} className="space-y-4">
              {highlights.map((highlight, index) => (
                <motion.div
                  key={index}
                  whileHover={{ x: 10, scale: 1.02 }}
                  className="flex items-start gap-4 p-4 rounded-lg bg-primary-aqua/5 border border-primary-aqua/20 hover:border-primary-aqua/40 transition-all duration-300"
                >
                  <div className="text-primary-aqua mt-1">
                    {highlight.icon}
                  </div>
                  <div>
                    <h3 className="text-xl font-montserrat font-semibold text-primary-light mb-2">
                      {highlight.title}
                    </h3>
                    <p className="text-primary-light/70">
                      {highlight.description}
                    </p>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
