@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap');

:root {
  --background: #1a2e45;
  --foreground: #f5f5f5;
  --primary-aqua: #4ecdc4;
  --primary-dark: #1a2e45;
  --primary-light: #f5f5f5;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: linear-gradient(135deg, var(--primary-dark) 0%, #0f1419 100%);
  color: var(--foreground);
  font-family: 'Roboto', sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--primary-dark);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-aqua);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #3bb5ad;
}

/* Glow effects */
.glow-effect {
  box-shadow: 0 0 20px rgba(78, 205, 196, 0.3);
  transition: box-shadow 0.3s ease;
}

.glow-effect:hover {
  box-shadow: 0 0 30px rgba(78, 205, 196, 0.5);
}

/* Button animations */
.btn-primary {
  background: linear-gradient(45deg, var(--primary-aqua), #3bb5ad);
  color: var(--primary-dark);
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(78, 205, 196, 0.4);
}

/* Section spacing */
.section-padding {
  padding: 100px 0;
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(45deg, var(--primary-aqua), #ffffff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
