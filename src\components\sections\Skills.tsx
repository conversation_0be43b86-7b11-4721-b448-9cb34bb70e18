'use client';

import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { 
  Microscope, 
  FlaskConical, 
  Dna, 
  Computer, 
  Users, 
  Target,
  Beaker,
  BarChart3,
  FileText,
  Lightbulb
} from 'lucide-react';
import Scene3D from '../3d/Scene3D';

const skillCategories = [
  {
    title: "Technical Skills",
    icon: <Microscope className="w-6 h-6" />,
    skills: [
      { name: "ELISA", icon: <Beaker className="w-5 h-5" /> },
      { name: "ICP-OES", icon: <BarChart3 className="w-5 h-5" /> },
      { name: "DNA/RNA Isolation", icon: <Dna className="w-5 h-5" /> },
      { name: "Plant Tissue Culture", icon: <FlaskConical className="w-5 h-5" /> },
      { name: "Microscopy", icon: <Microscope className="w-5 h-5" /> },
      { name: "Microbial Culture", icon: <Beaker className="w-5 h-5" /> }
    ]
  },
  {
    title: "Software & Tools",
    icon: <Computer className="w-6 h-6" />,
    skills: [
      { name: "MS Office Suite", icon: <FileText className="w-5 h-5" /> },
      { name: "R Programming", icon: <BarChart3 className="w-5 h-5" /> },
      { name: "Data Conversion", icon: <Computer className="w-5 h-5" /> },
      { name: "Statistical Analysis", icon: <BarChart3 className="w-5 h-5" /> },
      { name: "Research Documentation", icon: <FileText className="w-5 h-5" /> },
      { name: "Laboratory Management", icon: <Target className="w-5 h-5" /> }
    ]
  },
  {
    title: "Soft Skills",
    icon: <Users className="w-6 h-6" />,
    skills: [
      { name: "Team Collaboration", icon: <Users className="w-5 h-5" /> },
      { name: "Project Organization", icon: <Target className="w-5 h-5" /> },
      { name: "Problem Solving", icon: <Lightbulb className="w-5 h-5" /> },
      { name: "Research Planning", icon: <FileText className="w-5 h-5" /> },
      { name: "Data Analysis", icon: <BarChart3 className="w-5 h-5" /> },
      { name: "Scientific Communication", icon: <Users className="w-5 h-5" /> }
    ]
  }
];

export default function Skills() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const categoryVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        staggerChildren: 0.1,
      },
    },
  };

  const skillVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  return (
    <section id="skills" className="relative section-padding bg-gradient-to-b from-primary-dark to-primary-dark/90">
      {/* Subtle 3D Background */}
      <div className="absolute inset-0 opacity-10">
        <Scene3D showDNA={false} showParticles={true} />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-montserrat font-bold text-primary-light mb-6">
            Skills & <span className="gradient-text">Expertise</span>
          </h2>
          <p className="text-lg text-primary-light/70 max-w-2xl mx-auto">
            Comprehensive skill set spanning laboratory techniques, data analysis, and research methodologies
          </p>
        </motion.div>

        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="grid md:grid-cols-3 gap-8"
        >
          {skillCategories.map((category, categoryIndex) => (
            <motion.div
              key={categoryIndex}
              variants={categoryVariants}
              className="bg-primary-aqua/5 border border-primary-aqua/20 rounded-xl p-6 hover:border-primary-aqua/40 transition-all duration-300 glow-effect"
            >
              {/* Category Header */}
              <div className="flex items-center gap-3 mb-6">
                <div className="text-primary-aqua">
                  {category.icon}
                </div>
                <h3 className="text-xl font-montserrat font-bold text-primary-light">
                  {category.title}
                </h3>
              </div>

              {/* Skills Grid */}
              <div className="grid grid-cols-2 gap-3">
                {category.skills.map((skill, skillIndex) => (
                  <motion.div
                    key={skillIndex}
                    variants={skillVariants}
                    whileHover={{ 
                      scale: 1.05, 
                      y: -2,
                      boxShadow: "0 10px 25px rgba(78, 205, 196, 0.2)"
                    }}
                    className="bg-primary-dark/50 border border-primary-aqua/10 rounded-lg p-3 hover:border-primary-aqua/30 transition-all duration-300 cursor-pointer group"
                  >
                    <div className="flex items-center gap-2">
                      <div className="text-primary-aqua group-hover:scale-110 transition-transform duration-300">
                        {skill.icon}
                      </div>
                      <span className="text-sm font-medium text-primary-light group-hover:text-primary-aqua transition-colors duration-300">
                        {skill.name}
                      </span>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Category Footer */}
              <motion.div
                initial={{ width: 0 }}
                animate={inView ? { width: "100%" } : { width: 0 }}
                transition={{ duration: 1, delay: categoryIndex * 0.2 + 0.5 }}
                className="h-0.5 bg-gradient-to-r from-primary-aqua to-transparent mt-6"
              />
            </motion.div>
          ))}
        </motion.div>

        {/* Skills Summary */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          className="mt-16 text-center"
        >
          <div className="bg-primary-aqua/5 border border-primary-aqua/20 rounded-xl p-8 max-w-4xl mx-auto">
            <h3 className="text-2xl font-montserrat font-bold text-primary-light mb-4">
              Research Focus Areas
            </h3>
            <div className="flex flex-wrap justify-center gap-3">
              {[
                "Environmental Biotechnology",
                "Public Health Research", 
                "Virology Studies",
                "Trace Metal Analysis",
                "Medical Research",
                "Data Analytics"
              ].map((area, index) => (
                <motion.span
                  key={index}
                  initial={{ opacity: 0, scale: 0 }}
                  animate={inView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 + 0.8 }}
                  className="px-4 py-2 bg-primary-aqua/10 border border-primary-aqua/30 rounded-full text-primary-aqua font-medium hover:bg-primary-aqua/20 transition-colors duration-300"
                >
                  {area}
                </motion.span>
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
