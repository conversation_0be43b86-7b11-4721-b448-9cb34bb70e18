# Deployment Guide

## 🚀 Vercel Deployment (Recommended)

### Prerequisites
- GitHub account
- Vercel account (free tier available)

### Steps

1. **Push to GitHub**
   ```bash
   git init
   git add .
   git commit -m "Initial commit: <PERSON><PERSON><PERSON><PERSON>"
   git branch -M main
   git remote add origin <your-github-repo-url>
   git push -u origin main
   ```

2. **Deploy to Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Click "New Project"
   - Import your GitHub repository
   - Vercel will automatically detect Next.js and configure build settings
   - Click "Deploy"

3. **Custom Domain (Optional)**
   - In Vercel dashboard, go to your project
   - Navigate to "Settings" > "Domains"
   - Add your custom domain

### Environment Variables
No environment variables are required for the basic setup.

## 📁 File Replacements for Production

### 1. Profile Image
Replace `public/profile.svg` with:
- High-quality professional photo (JPG/PNG)
- Recommended size: 400x400px minimum
- Square aspect ratio for best results

### 2. Resume File
Replace `public/resume-placeholder.txt` with:
- Professional PDF resume
- Name it `resume.pdf`
- Update all links in components to point to `/resume.pdf`

### 3. Contact Information
Update the following in `src/components/sections/Contact.tsx`:
- Email address
- Phone number
- Location
- Social media links (LinkedIn, GitHub)

## 🔧 Customization Guide

### Personal Information
Update content in these files:
- `src/components/sections/Hero.tsx` - Name, roles, description
- `src/components/sections/About.tsx` - Bio, highlights, experience
- `src/components/sections/Experience.tsx` - Work history, positions
- `src/components/sections/Skills.tsx` - Technical and soft skills
- `src/components/sections/Education.tsx` - Degrees, certifications
- `src/components/sections/Projects.tsx` - Research projects, achievements

### SEO Optimization
Update `src/app/layout.tsx`:
- Title and description
- Keywords
- Open Graph metadata

### Color Scheme (Optional)
Modify `tailwind.config.ts` to change:
- Primary colors
- Gradient combinations
- Animation timings

## 🧪 Testing Before Deployment

1. **Development Server**
   ```bash
   npm run dev
   ```
   Test all sections and animations at http://localhost:3000

2. **Type Checking**
   ```bash
   npx tsc --noEmit
   ```

3. **Linting**
   ```bash
   npm run lint
   ```

4. **Build Test** (if permissions allow)
   ```bash
   npm run build
   ```

## 📱 Mobile Responsiveness

The website is fully responsive and tested on:
- Desktop (1920px+)
- Laptop (1024px - 1919px)
- Tablet (768px - 1023px)
- Mobile (320px - 767px)

## 🎨 3D Performance

The 3D elements are optimized for performance:
- Particle count is balanced for smooth animation
- DNA helix uses efficient geometry
- Automatic quality adjustment based on device capabilities

## 🔍 SEO Features

- Semantic HTML structure
- Proper heading hierarchy
- Alt text for images
- Meta descriptions
- Open Graph tags
- Fast loading times

## 📊 Analytics (Optional)

To add Google Analytics:
1. Create Google Analytics account
2. Add tracking code to `src/app/layout.tsx`
3. Set up conversion tracking for contact form

## 🛠️ Maintenance

### Regular Updates
- Keep dependencies updated: `npm update`
- Update content as career progresses
- Add new projects and achievements
- Refresh profile photo annually

### Performance Monitoring
- Use Vercel Analytics (built-in)
- Monitor Core Web Vitals
- Check mobile performance regularly

## 🆘 Troubleshooting

### Common Issues

1. **Build Errors on Windows**
   - Permission issues with `.next` folder
   - Solution: Deploy directly to Vercel (bypasses local build)

2. **3D Elements Not Loading**
   - Check browser WebGL support
   - Fallback: Disable 3D elements for older browsers

3. **Slow Loading**
   - Optimize images (use WebP format)
   - Enable Vercel's automatic optimizations

### Support
For technical issues:
1. Check Vercel deployment logs
2. Review browser console for errors
3. Test in different browsers and devices

---

**Ready for deployment! 🚀**
