'use client';

import { useRef, useMemo } from 'react';
import { useFrame } from '@react-three/fiber';
import { Sphere, Cylinder } from '@react-three/drei';
import * as THREE from 'three';

interface DNAHelixProps {
  position?: [number, number, number];
  scale?: number;
  speed?: number;
}

export default function DNAHelix({ position = [0, 0, 0], scale = 1, speed = 1 }: DNAHelixProps) {
  const groupRef = useRef<THREE.Group>(null);
  
  // Generate DNA helix points
  const helixData = useMemo(() => {
    const points: Array<{
      position: [number, number, number];
      color: string;
      strand: number;
    }> = [];
    
    const height = 8;
    const radius = 1.5;
    const turns = 3;
    const pointsPerTurn = 20;
    const totalPoints = turns * pointsPerTurn;
    
    for (let i = 0; i < totalPoints; i++) {
      const t = i / totalPoints;
      const angle = t * Math.PI * 2 * turns;
      const y = (t - 0.5) * height;
      
      // First strand
      const x1 = Math.cos(angle) * radius;
      const z1 = Math.sin(angle) * radius;
      points.push({
        position: [x1, y, z1],
        color: '#4ecdc4',
        strand: 1
      });
      
      // Second strand (opposite)
      const x2 = Math.cos(angle + Math.PI) * radius;
      const z2 = Math.sin(angle + Math.PI) * radius;
      points.push({
        position: [x2, y, z2],
        color: '#ffffff',
        strand: 2
      });
    }
    
    return points;
  }, []);
  
  // Generate connecting bonds
  const bonds = useMemo(() => {
    const bondData: Array<{
      start: [number, number, number];
      end: [number, number, number];
    }> = [];
    
    for (let i = 0; i < helixData.length - 2; i += 2) {
      const point1 = helixData[i];
      const point2 = helixData[i + 1];
      
      if (point1.strand !== point2.strand) {
        bondData.push({
          start: point1.position,
          end: point2.position
        });
      }
    }
    
    return bondData;
  }, [helixData]);
  
  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y += 0.01 * speed;
      groupRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.2;
    }
  });
  
  return (
    <group ref={groupRef} position={position} scale={scale}>
      {/* DNA base pairs */}
      {helixData.map((point, index) => (
        <Sphere key={index} position={point.position} args={[0.1, 8, 8]}>
          <meshStandardMaterial 
            color={point.color} 
            emissive={point.color}
            emissiveIntensity={0.2}
          />
        </Sphere>
      ))}
      
      {/* Connecting bonds */}
      {bonds.map((bond, index) => {
        const start = new THREE.Vector3(...bond.start);
        const end = new THREE.Vector3(...bond.end);
        const distance = start.distanceTo(end);
        const center = start.clone().add(end).multiplyScalar(0.5);
        
        return (
          <Cylinder
            key={`bond-${index}`}
            position={[center.x, center.y, center.z]}
            args={[0.02, 0.02, distance, 8]}
            rotation={[
              Math.atan2(end.z - start.z, end.y - start.y),
              0,
              -Math.atan2(end.x - start.x, end.y - start.y)
            ]}
          >
            <meshStandardMaterial 
              color="#ffffff" 
              opacity={0.6} 
              transparent 
            />
          </Cylinder>
        );
      })}
      
      {/* Ambient glow */}
      <pointLight 
        position={[0, 0, 0]} 
        color="#4ecdc4" 
        intensity={0.5} 
        distance={10} 
      />
    </group>
  );
}
