# <PERSON><PERSON><PERSON><PERSON> - Biotechnology Portfolio

A modern, professional portfolio website for <PERSON><PERSON><PERSON><PERSON>, showcasing her expertise in biotechnology research and professional experience at ICMR institutions.

## 🌟 Features

- **Modern Design**: Clean, professional, and futuristic design with scientific aesthetics
- **3D Interactive Background**: DNA helix and particle systems using React Three Fiber
- **Smooth Animations**: Framer Motion animations with scroll-triggered effects
- **Responsive Design**: Perfect alignment on desktop, tablet, and mobile devices
- **Scientific Color Palette**: Dark blue (#1a2e45), Aqua green (#4ecdc4), and White (#f5f5f5)
- **Professional Typography**: Montserrat for headings, Roboto for body text

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with TypeScript
- **Styling**: TailwindCSS with custom design system
- **Animations**: Framer Motion for smooth transitions
- **3D Graphics**: React Three Fiber + Drei for interactive backgrounds
- **Icons**: Lucide React for consistent iconography
- **Deployment**: Vercel-ready configuration

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd 0shi-portfolio
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

## 📁 Project Structure

```
src/
├── app/                    # Next.js app directory
│   ├── globals.css        # Global styles and Tailwind imports
│   ├── layout.tsx         # Root layout with fonts and metadata
│   └── page.tsx           # Main page component
├── components/
│   ├── 3d/                # React Three Fiber components
│   │   ├── DNAHelix.tsx   # Interactive DNA helix
│   │   ├── ParticleField.tsx # Particle system
│   │   └── Scene3D.tsx    # Main 3D scene wrapper
│   ├── sections/          # Page sections
│   │   ├── Hero.tsx       # Landing section with 3D background
│   │   ├── About.tsx      # About me section
│   │   ├── Experience.tsx # Professional timeline
│   │   ├── Skills.tsx     # Skills and expertise
│   │   ├── Education.tsx  # Academic qualifications
│   │   ├── Projects.tsx   # Research projects
│   │   └── Contact.tsx    # Contact form and information
│   └── ui/                # UI components
│       ├── Navbar.tsx     # Sticky navigation
│       └── Footer.tsx     # Site footer
public/
├── profile.svg            # Profile image placeholder
├── resume-placeholder.txt # Resume download file
└── ...                    # Other static assets
```

## 🎨 Customization

### Colors
The color palette is defined in `tailwind.config.ts`:
- Primary Dark: `#1a2e45`
- Primary Aqua: `#4ecdc4`
- Primary Light: `#f5f5f5`

### Content
Update the following files to customize content:
- Personal information in each section component
- Resume file in `public/` directory
- Profile image in `public/profile.svg`

### 3D Elements
Modify 3D components in `src/components/3d/`:
- `DNAHelix.tsx`: Customize DNA structure and animations
- `ParticleField.tsx`: Adjust particle count and behavior
- `Scene3D.tsx`: Control lighting and scene composition

## 📱 Sections Overview

1. **Hero**: Full-screen landing with animated DNA helix background
2. **About**: Personal introduction with profile image and highlights
3. **Experience**: Professional timeline with ICMR positions
4. **Skills**: Technical, software, and soft skills in organized categories
5. **Education**: Academic qualifications and certifications
6. **Projects**: Research projects and scientific work
7. **Contact**: Contact form and professional information

## 🚀 Deployment

### Vercel (Recommended)
1. Push code to GitHub repository
2. Connect repository to Vercel
3. Deploy automatically with zero configuration

### Manual Build
```bash
npm run build
npm start
```

## 📄 License

This project is created for Pratibha Madhukar's professional portfolio.

## 🤝 Contributing

This is a personal portfolio project. For suggestions or improvements, please reach out directly.

---

**Built with ❤️ for biotechnology research**
