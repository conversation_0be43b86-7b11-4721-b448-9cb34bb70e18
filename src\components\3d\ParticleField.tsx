'use client';

import { useRef, useMemo } from 'react';
import { useFrame } from '@react-three/fiber';
import { Points, PointMaterial } from '@react-three/drei';
import * as THREE from 'three';

interface ParticleFieldProps {
  count?: number;
  radius?: number;
}

export default function ParticleField({ count = 1000, radius = 10 }: ParticleFieldProps) {
  const pointsRef = useRef<THREE.Points>(null);
  
  const particlesPosition = useMemo(() => {
    const positions = new Float32Array(count * 3);
    
    for (let i = 0; i < count; i++) {
      const i3 = i * 3;
      
      // Create particles in a sphere
      const spherical = new THREE.Spherical(
        radius * Math.random(),
        Math.acos(2 * Math.random() - 1),
        Math.random() * Math.PI * 2
      );
      
      const vector = new THREE.Vector3().setFromSpherical(spherical);
      
      positions[i3] = vector.x;
      positions[i3 + 1] = vector.y;
      positions[i3 + 2] = vector.z;
    }
    
    return positions;
  }, [count, radius]);
  
  useFrame((state) => {
    if (pointsRef.current) {
      pointsRef.current.rotation.x = state.clock.elapsedTime * 0.05;
      pointsRef.current.rotation.y = state.clock.elapsedTime * 0.1;
    }
  });
  
  return (
    <Points ref={pointsRef} positions={particlesPosition} stride={3} frustumCulled={false}>
      <PointMaterial
        transparent
        color="#4ecdc4"
        size={0.05}
        sizeAttenuation={true}
        depthWrite={false}
        opacity={0.6}
      />
    </Points>
  );
}
