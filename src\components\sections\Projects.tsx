'use client';

import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { 
  FlaskConical, 
  Microscope, 
  BarChart3, 
  Dna, 
  Leaf, 
  Shield,
  ExternalLink,
  Calendar
} from 'lucide-react';

const projects = [
  {
    title: "ELISA-ICP-OES Integration for Trace Metal Analysis",
    category: "Analytical Research",
    icon: <BarChart3 className="w-6 h-6" />,
    description: "Developed an integrated approach combining ELISA and ICP-OES techniques for comprehensive trace metal analysis in biological samples.",
    technologies: ["ELISA", "ICP-OES", "Data Analysis", "Quality Control"],
    highlights: [
      "Novel integration methodology",
      "Enhanced analytical precision",
      "Reduced analysis time by 40%",
      "Improved detection limits"
    ],
    period: "2023-2024",
    status: "Completed"
  },
  {
    title: "Virology & Public Health Research",
    category: "Medical Research",
    icon: <Shield className="w-6 h-6" />,
    description: "Comprehensive research project at ICMR-NIV focusing on viral pathogen detection and public health implications.",
    technologies: ["Virology", "PCR", "Data Analysis", "Public Health"],
    highlights: [
      "Pathogen identification protocols",
      "Public health impact assessment",
      "Research data compilation",
      "Team collaboration"
    ],
    period: "2025-Present",
    status: "Ongoing"
  },
  {
    title: "Medical & Occupational Health Research",
    category: "Health Sciences",
    icon: <Microscope className="w-6 h-6" />,
    description: "Research internship at ICMR-ROHC focusing on occupational health hazards and medical research methodologies.",
    technologies: ["Medical Research", "Statistical Analysis", "Health Assessment", "Data Collection"],
    highlights: [
      "Occupational hazard analysis",
      "Health data collection",
      "Statistical interpretation",
      "Research documentation"
    ],
    period: "2024",
    status: "Completed"
  },
  {
    title: "Plant Tissue Culture Development",
    category: "Biotechnology",
    icon: <Leaf className="w-6 h-6" />,
    description: "Advanced plant tissue culture research focusing on optimization of growth media and propagation techniques.",
    technologies: ["Tissue Culture", "Plant Biology", "Media Optimization", "Sterile Techniques"],
    highlights: [
      "Media composition optimization",
      "Contamination control protocols",
      "Growth rate enhancement",
      "Propagation efficiency improvement"
    ],
    period: "2022-2023",
    status: "Completed"
  },
  {
    title: "Environmental Biotechnology Applications",
    category: "Environmental Science",
    icon: <Dna className="w-6 h-6" />,
    description: "Research on biotechnological solutions for environmental challenges including bioremediation and waste treatment.",
    technologies: ["Bioremediation", "Environmental Analysis", "Microbiology", "Sustainability"],
    highlights: [
      "Bioremediation protocols",
      "Environmental impact assessment",
      "Microbial analysis",
      "Sustainable solutions"
    ],
    period: "2021-2022",
    status: "Completed"
  },
  {
    title: "Advanced Laboratory Techniques Training",
    category: "Skill Development",
    icon: <FlaskConical className="w-6 h-6" />,
    description: "Comprehensive training program covering advanced laboratory techniques and quality control measures.",
    technologies: ["Laboratory Management", "Quality Control", "Instrumentation", "Safety Protocols"],
    highlights: [
      "Advanced instrumentation training",
      "Quality assurance protocols",
      "Safety standard implementation",
      "Laboratory efficiency optimization"
    ],
    period: "2023-2024",
    status: "Completed"
  }
];

export default function Projects() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
      },
    },
  };

  return (
    <section id="projects" className="section-padding bg-gradient-to-b from-primary-dark to-primary-dark/90">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-montserrat font-bold text-primary-light mb-6">
            Projects & <span className="gradient-text">Research</span>
          </h2>
          <p className="text-lg text-primary-light/70 max-w-2xl mx-auto">
            Comprehensive portfolio of research projects and scientific investigations
          </p>
        </motion.div>

        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {projects.map((project, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              whileHover={{ scale: 1.03, y: -10 }}
              className="bg-primary-aqua/5 border border-primary-aqua/20 rounded-xl p-6 hover:border-primary-aqua/40 transition-all duration-300 glow-effect group"
            >
              {/* Project Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="text-primary-aqua group-hover:scale-110 transition-transform duration-300">
                  {project.icon}
                </div>
                <div className="text-right">
                  <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${
                    project.status === 'Ongoing' 
                      ? 'bg-green-500/20 text-green-400 border border-green-500/30'
                      : 'bg-blue-500/20 text-blue-400 border border-blue-500/30'
                  }`}>
                    {project.status}
                  </span>
                </div>
              </div>

              {/* Project Title & Category */}
              <div className="mb-4">
                <h3 className="text-xl font-montserrat font-bold text-primary-light mb-2 group-hover:text-primary-aqua transition-colors duration-300">
                  {project.title}
                </h3>
                <div className="flex items-center gap-2 text-primary-aqua/80 text-sm">
                  <Calendar size={14} />
                  <span>{project.period}</span>
                </div>
                <p className="text-primary-aqua/80 text-sm font-medium mt-1">
                  {project.category}
                </p>
              </div>

              {/* Description */}
              <p className="text-primary-light/80 text-sm leading-relaxed mb-4">
                {project.description}
              </p>

              {/* Technologies */}
              <div className="mb-4">
                <div className="flex flex-wrap gap-2">
                  {project.technologies.map((tech, techIndex) => (
                    <span
                      key={techIndex}
                      className="px-2 py-1 bg-primary-aqua/10 border border-primary-aqua/30 rounded text-xs text-primary-aqua"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
              </div>

              {/* Highlights */}
              <div className="mb-6">
                <h4 className="text-sm font-semibold text-primary-light mb-2">Key Highlights:</h4>
                <ul className="space-y-1">
                  {project.highlights.map((highlight, highlightIndex) => (
                    <li key={highlightIndex} className="text-primary-light/70 text-xs flex items-start gap-2">
                      <span className="text-primary-aqua mt-1 text-xs">●</span>
                      <span>{highlight}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Project Footer */}
              <div className="pt-4 border-t border-primary-aqua/20">
                <motion.button
                  whileHover={{ x: 5 }}
                  className="flex items-center gap-2 text-primary-aqua hover:text-primary-light transition-colors duration-300 text-sm font-medium"
                >
                  <span>View Details</span>
                  <ExternalLink size={14} />
                </motion.button>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Research Focus Summary */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          className="mt-16 text-center"
        >
          <div className="bg-primary-aqua/5 border border-primary-aqua/20 rounded-xl p-8 max-w-4xl mx-auto">
            <h3 className="text-2xl font-montserrat font-bold text-primary-light mb-4">
              Research Impact
            </h3>
            <div className="grid md:grid-cols-3 gap-6 text-center">
              <div>
                <div className="text-3xl font-bold text-primary-aqua mb-2">6+</div>
                <div className="text-primary-light/70">Research Projects</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-primary-aqua mb-2">3+</div>
                <div className="text-primary-light/70">Years Experience</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-primary-aqua mb-2">2</div>
                <div className="text-primary-light/70">ICMR Institutions</div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
