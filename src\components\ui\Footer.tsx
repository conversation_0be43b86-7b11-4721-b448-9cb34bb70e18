'use client';

import { motion } from 'framer-motion';
import { Heart, ArrowUp } from 'lucide-react';

export default function Footer() {
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <footer className="relative bg-primary-dark border-t border-primary-aqua/20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col md:flex-row justify-between items-center">
          {/* Left Side - Copyright */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center md:text-left mb-4 md:mb-0"
          >
            <p className="text-primary-light/70 text-sm">
              © 2024 Pratibha <PERSON>. All rights reserved.
            </p>
            <p className="text-primary-light/50 text-xs mt-1 flex items-center justify-center md:justify-start gap-1">
              Made with <Heart className="w-3 h-3 text-primary-aqua" fill="currentColor" /> for biotechnology research
            </p>
          </motion.div>

          {/* Center - Name */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="mb-4 md:mb-0"
          >
            <h3 className="text-xl font-montserrat font-bold gradient-text">
              Pratibha Madhukar
            </h3>
            <p className="text-primary-light/60 text-sm text-center">
              Biotechnologist & Researcher
            </p>
          </motion.div>

          {/* Right Side - Back to Top */}
          <motion.button
            onClick={scrollToTop}
            whileHover={{ scale: 1.1, y: -2 }}
            whileTap={{ scale: 0.9 }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="w-12 h-12 bg-primary-aqua/10 border border-primary-aqua/30 rounded-full flex items-center justify-center text-primary-aqua hover:bg-primary-aqua hover:text-primary-dark transition-all duration-300 glow-effect"
          >
            <ArrowUp size={20} />
          </motion.button>
        </div>

        {/* Bottom Border */}
        <motion.div
          initial={{ width: 0 }}
          whileInView={{ width: "100%" }}
          transition={{ duration: 1, delay: 0.6 }}
          className="h-0.5 bg-gradient-to-r from-transparent via-primary-aqua to-transparent mt-6"
        />
      </div>
    </footer>
  );
}
