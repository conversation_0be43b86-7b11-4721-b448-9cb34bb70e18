'use client';

import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { Calendar, MapPin, Building } from 'lucide-react';

const experiences = [
  {
    title: "Project Assistant",
    company: "ICMR-NIV, Bengaluru",
    period: "June 2025 – Present",
    location: "Bengaluru, India",
    description: [
      "Conducting advanced research in virology and public health",
      "Data analysis and interpretation of research findings",
      "Team coordination and project management",
      "Laboratory quality control and maintenance"
    ],
    type: "current"
  },
  {
    title: "Research Intern",
    company: "ICMR-ROHC, Bengaluru", 
    period: "January 2024 – June 2024",
    location: "Bengaluru, India",
    description: [
      "Medical and occupational health research",
      "Data collection and statistical analysis",
      "Research facility training under DST-SERB",
      "Collaborative research projects"
    ],
    type: "past"
  },
  {
    title: "Academic Research Projects",
    company: "St. Thomas College, Bhilai",
    period: "2019 – 2024",
    location: "Bhilai, India", 
    description: [
      "Plant tissue culture research and development",
      "Trace metal analysis using ICP-OES",
      "ELISA-ICP-OES integration studies",
      "Environmental biotechnology applications"
    ],
    type: "academic"
  }
];

export default function Experience() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, x: -50 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.8,
      },
    },
  };

  return (
    <section id="experience" className="section-padding bg-gradient-to-b from-primary-dark/90 to-primary-dark">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-montserrat font-bold text-primary-light mb-6">
            Professional <span className="gradient-text">Experience</span>
          </h2>
          <p className="text-lg text-primary-light/70 max-w-2xl mx-auto">
            My journey in biotechnology research and professional development
          </p>
        </motion.div>

        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="relative"
        >
          {/* Timeline Line */}
          <div className="absolute left-8 md:left-1/2 transform md:-translate-x-1/2 h-full w-0.5 bg-gradient-to-b from-primary-aqua to-primary-aqua/30"></div>

          {/* Experience Items */}
          <div className="space-y-12">
            {experiences.map((exp, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className={`relative flex items-center ${
                  index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'
                } flex-col md:flex-row`}
              >
                {/* Timeline Dot */}
                <div className="absolute left-8 md:left-1/2 transform md:-translate-x-1/2 -translate-y-1/2 top-1/2">
                  <div className={`w-4 h-4 rounded-full border-4 ${
                    exp.type === 'current' 
                      ? 'bg-primary-aqua border-primary-aqua shadow-lg shadow-primary-aqua/50' 
                      : 'bg-primary-dark border-primary-aqua'
                  }`}></div>
                </div>

                {/* Content Card */}
                <motion.div
                  whileHover={{ scale: 1.02, y: -5 }}
                  className={`w-full md:w-5/12 ${
                    index % 2 === 0 ? 'md:mr-auto md:pr-8' : 'md:ml-auto md:pl-8'
                  } ml-16 md:ml-0`}
                >
                  <div className="bg-primary-aqua/5 border border-primary-aqua/20 rounded-lg p-6 hover:border-primary-aqua/40 transition-all duration-300 glow-effect">
                    {/* Header */}
                    <div className="mb-4">
                      <div className="flex items-center gap-2 text-primary-aqua mb-2">
                        <Calendar size={16} />
                        <span className="text-sm font-medium">{exp.period}</span>
                      </div>
                      
                      <h3 className="text-xl font-montserrat font-bold text-primary-light mb-1">
                        {exp.title}
                      </h3>
                      
                      <div className="flex items-center gap-4 text-primary-light/70">
                        <div className="flex items-center gap-1">
                          <Building size={16} />
                          <span className="font-medium">{exp.company}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <MapPin size={16} />
                          <span>{exp.location}</span>
                        </div>
                      </div>
                    </div>

                    {/* Description */}
                    <ul className="space-y-2">
                      {exp.description.map((item, idx) => (
                        <li key={idx} className="text-primary-light/80 flex items-start gap-2">
                          <span className="text-primary-aqua mt-2 text-xs">●</span>
                          <span>{item}</span>
                        </li>
                      ))}
                    </ul>

                    {/* Type Badge */}
                    <div className="mt-4">
                      <span className={`inline-block px-3 py-1 rounded-full text-xs font-medium ${
                        exp.type === 'current' 
                          ? 'bg-primary-aqua/20 text-primary-aqua border border-primary-aqua/30' 
                          : exp.type === 'past'
                          ? 'bg-blue-500/20 text-blue-400 border border-blue-500/30'
                          : 'bg-purple-500/20 text-purple-400 border border-purple-500/30'
                      }`}>
                        {exp.type === 'current' ? 'Current Position' : 
                         exp.type === 'past' ? 'Previous Role' : 'Academic Projects'}
                      </span>
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
}
