'use client';

import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { useState } from 'react';
import { 
  Mail, 
  Phone, 
  MapPin, 
  Send, 
  Download,
  Linkedin,
  Github,
  User,
  MessageSquare
} from 'lucide-react';
import Scene3D from '../3d/Scene3D';

export default function Contact() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission here
    console.log('Form submitted:', formData);
    // Reset form
    setFormData({ name: '', email: '', message: '' });
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
      },
    },
  };

  const contactInfo = [
    {
      icon: <Mail className="w-6 h-6" />,
      label: "Email",
      value: "<EMAIL>",
      href: "mailto:<EMAIL>"
    },
    {
      icon: <Phone className="w-6 h-6" />,
      label: "Phone",
      value: "+91 9691583220",
      href: "tel:+************"
    },
    {
      icon: <MapPin className="w-6 h-6" />,
      label: "Location",
      value: "Bengaluru, India",
      href: "#"
    }
  ];

  return (
    <section id="contact" className="relative section-padding bg-gradient-to-b from-primary-dark/90 to-primary-dark">
      {/* Subtle 3D Background */}
      <div className="absolute inset-0 opacity-20">
        <Scene3D showDNA={false} showParticles={true} />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-montserrat font-bold text-primary-light mb-6">
            Get In <span className="gradient-text">Touch</span>
          </h2>
          <p className="text-lg text-primary-light/70 max-w-2xl mx-auto">
            Let&apos;s discuss opportunities in biotechnology research and collaboration
          </p>
        </motion.div>

        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="grid lg:grid-cols-2 gap-12"
        >
          {/* Contact Information */}
          <motion.div variants={itemVariants} className="space-y-8">
            <div>
              <h3 className="text-2xl font-montserrat font-bold text-primary-light mb-6">
                Contact Information
              </h3>
              
              <div className="space-y-6">
                {contactInfo.map((info, index) => (
                  <motion.a
                    key={index}
                    href={info.href}
                    whileHover={{ x: 10, scale: 1.02 }}
                    className="flex items-center gap-4 p-4 bg-primary-aqua/5 border border-primary-aqua/20 rounded-lg hover:border-primary-aqua/40 transition-all duration-300 glow-effect group"
                  >
                    <div className="text-primary-aqua group-hover:scale-110 transition-transform duration-300">
                      {info.icon}
                    </div>
                    <div>
                      <p className="text-primary-light/70 text-sm">{info.label}</p>
                      <p className="text-primary-light font-medium">{info.value}</p>
                    </div>
                  </motion.a>
                ))}
              </div>
            </div>

            {/* Social Links & Resume */}
            <div>
              <h3 className="text-xl font-montserrat font-bold text-primary-light mb-4">
                Connect & Download
              </h3>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <motion.a
                  href="/resume-placeholder.txt"
                  download
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="btn-primary px-6 py-3 rounded-lg font-semibold flex items-center justify-center gap-3 glow-effect"
                >
                  <Download size={20} />
                  Download Resume
                </motion.a>
                
                <div className="flex gap-3">
                  <motion.a
                    href="#"
                    whileHover={{ scale: 1.1, y: -2 }}
                    className="w-12 h-12 bg-primary-aqua/10 border border-primary-aqua/30 rounded-lg flex items-center justify-center text-primary-aqua hover:bg-primary-aqua hover:text-primary-dark transition-all duration-300"
                  >
                    <Linkedin size={20} />
                  </motion.a>
                  
                  <motion.a
                    href="#"
                    whileHover={{ scale: 1.1, y: -2 }}
                    className="w-12 h-12 bg-primary-aqua/10 border border-primary-aqua/30 rounded-lg flex items-center justify-center text-primary-aqua hover:bg-primary-aqua hover:text-primary-dark transition-all duration-300"
                  >
                    <Github size={20} />
                  </motion.a>
                </div>
              </div>
            </div>

            {/* Professional Summary */}
            <div className="bg-primary-aqua/5 border border-primary-aqua/20 rounded-lg p-6">
              <h4 className="text-lg font-montserrat font-bold text-primary-light mb-3">
                Open to Opportunities
              </h4>
              <p className="text-primary-light/80 text-sm leading-relaxed">
                I&apos;m actively seeking opportunities in biotechnology research,
                environmental applications, and public health initiatives.
                Let&apos;s connect to discuss potential collaborations and projects.
              </p>
            </div>
          </motion.div>

          {/* Contact Form */}
          <motion.div variants={itemVariants}>
            <div className="bg-primary-aqua/5 border border-primary-aqua/20 rounded-xl p-8 glow-effect">
              <h3 className="text-2xl font-montserrat font-bold text-primary-light mb-6">
                Send a Message
              </h3>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label className="block text-primary-light/80 text-sm font-medium mb-2">
                    <User className="w-4 h-4 inline mr-2" />
                    Your Name
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-primary-dark/50 border border-primary-aqua/30 rounded-lg text-primary-light placeholder-primary-light/50 focus:border-primary-aqua focus:outline-none focus:ring-2 focus:ring-primary-aqua/20 transition-all duration-300"
                    placeholder="Enter your full name"
                  />
                </div>

                <div>
                  <label className="block text-primary-light/80 text-sm font-medium mb-2">
                    <Mail className="w-4 h-4 inline mr-2" />
                    Email Address
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-primary-dark/50 border border-primary-aqua/30 rounded-lg text-primary-light placeholder-primary-light/50 focus:border-primary-aqua focus:outline-none focus:ring-2 focus:ring-primary-aqua/20 transition-all duration-300"
                    placeholder="Enter your email address"
                  />
                </div>

                <div>
                  <label className="block text-primary-light/80 text-sm font-medium mb-2">
                    <MessageSquare className="w-4 h-4 inline mr-2" />
                    Message
                  </label>
                  <textarea
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    required
                    rows={5}
                    className="w-full px-4 py-3 bg-primary-dark/50 border border-primary-aqua/30 rounded-lg text-primary-light placeholder-primary-light/50 focus:border-primary-aqua focus:outline-none focus:ring-2 focus:ring-primary-aqua/20 transition-all duration-300 resize-none"
                    placeholder="Tell me about your project or opportunity..."
                  />
                </div>

                <motion.button
                  type="submit"
                  whileHover={{ scale: 1.02, y: -2 }}
                  whileTap={{ scale: 0.98 }}
                  className="w-full btn-primary px-6 py-4 rounded-lg font-semibold flex items-center justify-center gap-3 glow-effect"
                >
                  <Send size={20} />
                  Send Message
                </motion.button>
              </form>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
